<template>
  <div class="wai-she-tui-jian">
    <RtHeader title="外设推荐" />

    <!-- 搜索区域 -->
    <div class="search-area">
      <!-- 返回首页按钮 -->
      <div class="back-home-container">
        <el-button
          type="default"
          @click="goToHome"
          class="back-home-btn"
          :icon="House"
        >
          返回首页
        </el-button>
      </div>

      <!-- 搜索框 -->
      <div class="search-container">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索外设产品..."
          class="search-input"
          clearable
          @keyup.enter="handleSearchClick"
          @clear="handleClearSearch"
          @blur="handleInputBlur"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #append>
            <el-button type="primary" @click="handleSearchClick" :icon="Search">
              搜索
            </el-button>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 热销爆款 -->
    <div v-if="!isSearching" class="hot-sale-section">
      <h1 class="section-title">热销爆款</h1>
      <div class="hot-sale-productList">
        <el-row :gutter="20">
          <el-col :span="6" v-for="product in hotSaleProducts" :key="product.id">
            <el-card :body-style="{ padding: '0px' }" shadow="hover" @click="gotoProductDetail(product)">
              <img :src="product.imgUrl" class="product-image" alt="Product Image" />
              <div class="product-info">
                <p class="product-name">{{ product.name }}</p>
                <div class="product-bottom">
                  <span class="price">￥{{ product.price }}</span>
                  <span class="stock">库存: {{ product.stockQuantity }}</span>
                </div>
                <el-button type="primary" class="detail-button">查看详情</el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 搜索结果标题 -->
    <div v-if="isSearching" class="search-result-section">
      <h1 class="section-title">
        搜索结果
        <span v-if="searchKeyword" class="search-keyword">「{{ searchKeyword }}」</span>
      </h1>
    </div>

    <!-- 排序按钮 -->
    <div class="sort-section">
      <div class="sort-buttons">
        <el-button
          :type="sortType === 'stock' ? 'primary' : 'default'"
          @click="sortByStock"
          class="sort-btn"
        >
          销量 <span v-if="sortType === 'stock'" :class="['sort-arrow', sortOrder === 'desc' ? 'sort-arrow-down' : '']">^</span>
        </el-button>
        <el-button
          :type="sortType === 'price' ? 'primary' : 'default'"
          @click="sortByPrice"
          class="sort-btn"
        >
          价格 <span v-if="sortType === 'price'" :class="['sort-arrow', sortOrder === 'desc' ? 'sort-arrow-down' : '']">^</span>
        </el-button>

        <!-- 价格区间筛选 -->
        <el-dropdown @command="handlePriceRangeSelect" trigger="hover" class="price-range-dropdown">
          <el-button
            :type="selectedPriceRange ? 'primary' : 'default'"
            class="sort-btn"
          >
            区间 <span v-if="selectedPriceRange" class="range-text">{{ getPriceRangeText(selectedPriceRange) }}</span>
            <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="all">全部价格</el-dropdown-item>
              <el-dropdown-item command="0-100">0-100元</el-dropdown-item>
              <el-dropdown-item command="100-300">100-300元</el-dropdown-item>
              <el-dropdown-item command="300-1000">300-1000元</el-dropdown-item>
              <el-dropdown-item command="1000-3000">1000-3000元</el-dropdown-item>
              <el-dropdown-item command="3000+">3000元以上</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>



    <!-- 产品列表 -->
    <div class="product-list-section">
      <div v-if="sortedProducts.length === 0" class="no-products">
        <p>暂无外设产品</p>
      </div>
      <el-row :gutter="20" v-else>
        <el-col :span="6" v-for="product in sortedProducts" :key="product.id">
          <el-card :body-style="{ padding: '0px' }" shadow="hover" @click="gotoProductDetail(product)">
            <img :src="product.imgUrl" class="product-image" alt="Product Image" />
            <div class="product-info">
              <p class="product-name">{{ product.name }}</p>
              <div class="product-bottom">
                <span class="price">￥{{ product.price }}</span>
                <span class="stock">库存: {{ product.stockQuantity }}</span>
              </div>
              <el-button type="primary" class="detail-button">查看详情</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useProductStore } from '@/stores/product'
import { Search, ArrowDown, House } from '@element-plus/icons-vue'
import { orderBy } from 'lodash'
import RtHeader from '@/views/user/RtHeader.vue'

const router = useRouter()
const productStore = useProductStore()

// 响应式数据
const searchKeyword = ref('')
const sortType = ref('') // 'stock' 或 'price'
const sortOrder = ref('asc') // 'asc' 或 'desc'
const isSearching = ref(false) // 是否处于搜索状态
const selectedPriceRange = ref('') // 选中的价格区间

// 获取外设产品（根据数据库中的分类ID过滤）
const peripheralProducts = computed(() => {
  // 如果没有产品数据，返回空数组
  if (!productStore.products || productStore.products.length === 0) {
    return []
  }

  // 根据您提供的正确分类ID信息：
  // 键盘: 8, 鼠标: 9, 显示器: 10, 头戴式耳机: 13
  const peripheralCategoryIds = [8, 9, 10, 13] // 键盘、鼠标、显示器、头戴式耳机

  return productStore.products.filter(product => {
    // 首先根据categoryId过滤
    if (product.categoryId && peripheralCategoryIds.includes(product.categoryId)) {
      return true
    }

    // 如果没有categoryId，则根据产品名称包含关键词来过滤
    const peripheralKeywords = ['鼠标', '键盘', '显示器', '耳机', '音响', '摄像头', '麦克风', '手柄', 'HKC', '科睿']
    return peripheralKeywords.some(keyword => product.name && product.name.includes(keyword))
  })
})

// 价格区间过滤
const priceRangeFilteredProducts = computed(() => {
  if (!selectedPriceRange.value || selectedPriceRange.value === 'all') {
    return peripheralProducts.value
  }

  return peripheralProducts.value.filter(product => {
    const price = parseFloat(product.price)
    switch (selectedPriceRange.value) {
      case '0-100':
        return price >= 0 && price <= 100
      case '100-300':
        return price > 100 && price <= 300
      case '300-1000':
        return price > 300 && price <= 1000
      case '1000-3000':
        return price > 1000 && price <= 3000
      case '3000+':
        return price > 3000
      default:
        return true
    }
  })
})

// 搜索过滤
const filteredProducts = computed(() => {
  let products = priceRangeFilteredProducts.value

  if (isSearching.value && searchKeyword.value) {
    products = products.filter(product =>
      product.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  return products
})

// 排序后的产品
const sortedProducts = computed(() => {
  if (!sortType.value) {
    return filteredProducts.value
  }

  const sortKey = sortType.value === 'stock' ? 'stockQuantity' : 'price'
  return orderBy(filteredProducts.value, [sortKey], [sortOrder.value])
})

// 热销产品（取前4个库存较少的产品作为热销）
const hotSaleProducts = computed(() => {
  return orderBy(peripheralProducts.value, ['stockQuantity'], ['asc']).slice(0, 4)
})



// 方法
const handleSearchClick = () => {
  if (searchKeyword.value.trim()) {
    isSearching.value = true
  } else {
    isSearching.value = false
  }
}

const handleClearSearch = () => {
  searchKeyword.value = ''
  isSearching.value = false
}

const handleInputBlur = () => {
  // 当输入框失去焦点且为空时，退出搜索状态
  if (!searchKeyword.value.trim()) {
    isSearching.value = false
  }
}

const handlePriceRangeSelect = (command) => {
  if (command === 'all') {
    selectedPriceRange.value = ''
  } else {
    selectedPriceRange.value = command
  }
}

const getPriceRangeText = (range) => {
  switch (range) {
    case '0-100':
      return '0-100'
    case '100-300':
      return '100-300'
    case '300-1000':
      return '300-1000'
    case '1000-3000':
      return '1000-3000'
    case '3000+':
      return '3000+'
    default:
      return ''
  }
}

const sortByStock = () => {
  if (sortType.value === 'stock') {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortType.value = 'stock'
    sortOrder.value = 'asc'
  }
}

const sortByPrice = () => {
  if (sortType.value === 'price') {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortType.value = 'price'
    sortOrder.value = 'asc'
  }
}

const gotoProductDetail = (product) => {
  router.push({ name: 'ProductDetail', params: { id: product.id } })
}

const goToHome = () => {
  router.push('/')
}

// 生命周期
onMounted(async () => {
  // 获取产品列表
  await productStore.getProductList()
})
</script>

<style scoped>
.wai-she-tui-jian {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 搜索区域样式 */
.search-area {
  display: flex;
  align-items: center;
  gap: 20px;
  margin: 20px 0;
}

.back-home-container {
  flex-shrink: 0;
}

.back-home-btn {
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.back-home-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 搜索框样式 */
.search-container {
  flex: 1;
  display: flex;
  justify-content: center;
}

.search-input {
  max-width: 600px;
  width: 100%;
}

.search-input .el-input__inner {
  border-radius: 25px;
  padding-left: 45px;
  height: 45px;
  font-size: 16px;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
}

.search-input .el-input__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.2);
}

.search-input .el-input-group__append {
  border-radius: 0 25px 25px 0;
  border-left: none;
}

.search-input .el-input-group__append .el-button {
  border-radius: 0 23px 23px 0;
  height: 41px;
  font-weight: 500;
}

/* 热销爆款样式 */
.hot-sale-section {
  margin: 30px 0;
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 20px;
  text-align: center;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  border-radius: 2px;
}

/* 搜索结果样式 */
.search-result-section {
  margin: 30px 0;
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.search-keyword {
  color: #409eff;
  font-weight: normal;
  font-size: 20px;
}

/* 排序按钮样式 */
.sort-section {
  margin: 20px 0;
  display: flex;
  justify-content: flex-start;
}

.sort-buttons {
  display: flex;
  gap: 15px;
  background: white;
  padding: 15px 25px;
  border-radius: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sort-btn {
  min-width: 120px;
  height: 40px;
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.sort-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 排序箭头样式 */
.sort-arrow {
  display: inline-block;
  margin-left: 5px;
  font-weight: bold;
  font-size: 14px;
  transition: transform 0.3s ease;
  color: #409eff;
}

.sort-arrow-down {
  transform: rotate(180deg);
}

/* 价格区间下拉菜单样式 */
.price-range-dropdown {
  margin-left: 15px;
}

.dropdown-icon {
  margin-left: 5px;
  font-size: 12px;
  transition: transform 0.3s ease;
}

.range-text {
  margin-left: 5px;
  font-size: 12px;
  color: #409eff;
  font-weight: normal;
}

.el-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.el-dropdown-menu__item {
  padding: 10px 16px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.el-dropdown-menu__item:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

/* 产品列表样式 */
.product-list-section {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.hot-sale-productList {
  margin-bottom: 20px;
}

/* 产品卡片样式 */
.el-card {
  background-color: #ffffff;
  border-radius: 15px;
  margin-bottom: 20px;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.el-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.product-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.el-card:hover .product-image {
  transform: scale(1.05);
}

.product-info {
  padding: 15px;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 10px 0;
  height: 48px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 24px;
}

.product-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.price {
  font-size: 18px;
  font-weight: bold;
  color: #e6a23c;
}

.stock {
  font-size: 12px;
  color: #909399;
  background: #f4f4f5;
  padding: 2px 8px;
  border-radius: 10px;
}

.detail-button {
  width: 100%;
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.detail-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.no-products {
  text-align: center;
  padding: 40px;
  color: #909399;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .el-col {
    width: 33.333%;
  }
}

@media (max-width: 768px) {
  .el-col {
    width: 50%;
  }

  .wai-she-tui-jian {
    padding: 10px;
  }

  .search-area {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .search-container {
    width: 100%;
  }

  .sort-buttons {
    flex-direction: column;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .el-col {
    width: 100%;
  }
}
</style>